<?php
/**
 * Plugin to handle free shipping threshold method codes in admin order creation
 */
declare(strict_types=1);

namespace Coditron\CustomShippingRate\Plugin\Quote\Address\Total;

use Magento\Quote\Model\Quote\Address\Total\Shipping;
use Magento\Quote\Model\Quote;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote\Address\Total;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Coditron\CustomShippingRate\Model\Command\ProductDataBuilder;
use Psr\Log\LoggerInterface;

class FreeShippingPlugin
{
    /**
     * @var CollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var ProductDataBuilder
     */
    protected $productDataBuilder;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param CollectionFactory $shipTableRatesCollectionFactory
     * @param ProductDataBuilder $productDataBuilder
     * @param LoggerInterface $logger
     */
    public function __construct(
        CollectionFactory $shipTableRatesCollectionFactory,
        ProductDataBuilder $productDataBuilder,
        LoggerInterface $logger
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->productDataBuilder = $productDataBuilder;
        $this->logger = $logger;
    }

    /**
     * Handle free shipping threshold method codes
     *
     * @param Shipping $subject
     * @param callable $proceed
     * @param Quote $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @param Total $total
     * @return mixed
     */
    public function aroundCollect(
        Shipping $subject,
        callable $proceed,
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ) {
        $address = $shippingAssignment->getShipping()->getAddress();
        $method = $address->getShippingMethod();

        // Only process if method is not null and not empty
        if (!empty($method) && is_string($method)) {
            // Check if this is one of our custom free shipping methods
            if ($this->isCustomFreeShippingMethod($method)) {
                $this->handleCustomFreeShippingMethod($address, $method, $quote);
            } elseif ($this->isCustomRegularShippingMethod($method)) {
                $this->handleCustomRegularShippingMethod($address, $method, $quote);
            }
        }

        return $proceed($quote, $shippingAssignment, $total);
    }

    /**
     * Check if method is a custom free shipping method
     *
     * @param string|null $method
     * @return bool
     */
    protected function isCustomFreeShippingMethod(?string $method): bool
    {
        if (empty($method) || !is_string($method)) {
            return false;
        }
        return strpos($method, 'customshippingrate_freeshipping_') === 0;
    }

    /**
     * Check if method is a custom regular shipping method
     *
     * @param string|null $method
     * @return bool
     */
    protected function isCustomRegularShippingMethod(?string $method): bool
    {
        if (empty($method) || !is_string($method)) {
            return false;
        }
        return strpos($method, 'customshippingrate_regular_') === 0;
    }

    /**
     * Handle custom free shipping method
     *
     * @param $address
     * @param string $method
     * @param Quote $quote
     */
    protected function handleCustomFreeShippingMethod($address, string $method, Quote $quote)
    {
        try {
            // Extract seller ID from method code
            $sellerId = str_replace('customshippingrate_freeshipping_', '', $method);

            if (empty($sellerId) || !$address || !$quote) {
                return;
            }

            $this->logger->info(
                '[CoditronFreeShipping] Admin selected free shipping method',
                [
                    'method' => $method,
                    'seller_id' => $sellerId,
                    'quote_id' => $quote->getId()
                ]
            );

            // Update the shipping rate to free
            $this->updateShippingRate($address, 'customshippingrate_freeshipping', 0, 'Free Shipping (Threshold Applied)');
        } catch (\Exception $e) {
            $this->logger->error(
                '[CoditronFreeShipping] Error handling free shipping method: ' . $e->getMessage(),
                [
                    'method' => $method,
                    'trace' => $e->getTraceAsString()
                ]
            );
        }
    }

    /**
     * Handle custom regular shipping method
     *
     * @param $address
     * @param string $method
     * @param Quote $quote
     */
    protected function handleCustomRegularShippingMethod($address, string $method, Quote $quote)
    {
        try {
            // Extract seller ID from method code
            $sellerId = str_replace('customshippingrate_regular_', '', $method);

            if (empty($sellerId) || !$address || !$quote) {
                return;
            }

            // Get the actual shipping rate for this seller
            $shippingRate = $this->getSellerShippingRate($sellerId, $address->getCountryId(), $quote);

            $this->logger->info(
                '[CoditronRegularShipping] Admin selected regular shipping method',
                [
                    'method' => $method,
                    'seller_id' => $sellerId,
                    'shipping_rate' => $shippingRate,
                    'quote_id' => $quote->getId()
                ]
            );

            if ($shippingRate !== null) {
                $this->updateShippingRate($address, 'customshippingrate_regular', $shippingRate['price'], $shippingRate['title']);
            }
        } catch (\Exception $e) {
            $this->logger->error(
                '[CoditronRegularShipping] Error handling regular shipping method: ' . $e->getMessage(),
                [
                    'method' => $method,
                    'trace' => $e->getTraceAsString()
                ]
            );
        }
    }

    /**
     * Get seller shipping rate
     *
     * @param string $sellerId
     * @param string $country
     * @param Quote $quote
     * @return array|null
     */
    protected function getSellerShippingRate(string $sellerId, string $country, Quote $quote): ?array
    {
        try {
            if (empty($sellerId) || empty($country) || !$quote) {
                return null;
            }

            $items = $quote->getAllVisibleItems();
            if (empty($items)) {
                return null;
            }

            $toShipPerSellerArray = $this->productDataBuilder->build($items, $country);

            foreach ($toShipPerSellerArray as $sellerKey => $sellerData) {
                if (strpos($sellerKey, '|') === false) {
                    continue;
                }

                [$sellerIdentifier, $currentSellerId] = explode('|', $sellerKey);

                if ($currentSellerId == $sellerId) {
                    $collection = $this->shipTableRatesCollectionFactory->create()
                        ->addFieldToFilter('seller_id', ['eq' => $sellerId])
                        ->addFieldToFilter('countries', ['finset' => $country])
                        ->addFieldToFilter('weight', ['lteq' => $sellerData['total_weight']])
                        ->addFieldToFilter('min_order_amount', ['eq' => 0]) // Regular shipping only
                        ->setOrder('weight', 'DESC')
                        ->setPageSize(1);

                    $rate = $collection->getFirstItem();
                    if ($rate && $rate->getId()) {
                        return [
                            'price' => (float)$rate->getShippingPrice(),
                            'title' => $rate->getCourierName() . ' - ' . $rate->getServiceType()
                        ];
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error(
                '[CoditronShipping] Error getting seller shipping rate: ' . $e->getMessage(),
                [
                    'seller_id' => $sellerId,
                    'country' => $country,
                    'trace' => $e->getTraceAsString()
                ]
            );
        }

        return null;
    }

    /**
     * Update shipping rate
     *
     * @param $address
     * @param string $methodCode
     * @param float $price
     * @param string $title
     */
    protected function updateShippingRate($address, string $methodCode, float $price, string $title)
    {
        try {
            if (!$address || empty($methodCode) || empty($title)) {
                return;
            }

            // Update the shipping method code
            $address->setShippingMethod($methodCode);

            // Find and update the shipping rate
            $shippingRates = $address->getAllShippingRates();
            if (!empty($shippingRates)) {
                foreach ($shippingRates as $rate) {
                    if ($rate && strpos($rate->getCode(), 'customshippingrate') !== false) {
                        $rate->setPrice($price);
                        $rate->setCost($price);
                        $rate->setMethodTitle($title);
                        break;
                    }
                }
            }
        } catch (\Exception $e) {
            $this->logger->error(
                '[CoditronShipping] Error updating shipping rate: ' . $e->getMessage(),
                [
                    'method_code' => $methodCode,
                    'price' => $price,
                    'title' => $title,
                    'trace' => $e->getTraceAsString()
                ]
            );
        }
    }
}
