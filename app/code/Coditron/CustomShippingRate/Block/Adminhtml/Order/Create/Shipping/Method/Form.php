<?php
/**
 * Copyright © Coditron Technologies All rights reserved.
 * See COPYING.txt for license details.
 * http://www.coditron.com | <EMAIL>
 */

namespace Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Method;

use Magento\Quote\Model\Quote\Address\Rate;
use Coditron\CustomShippingRate\Model\Carrier;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory;
use Coditron\CustomShippingRate\Model\Command\ProductDataBuilder;
use Webkul\Marketplace\Helper\Data as MarketplaceHelper;

/**
 * Class Form
 * @package Coditron\CustomShippingRate\Block\Adminhtml\Order\Create\Shipping\Method
 */
class Form extends \Magento\Sales\Block\Adminhtml\Order\Create\Shipping\Method\Form
{
    /** @var Rate|false **/
    protected $activeMethodRate;

    /**
     * @var CollectionFactory
     */
    protected $shipTableRatesCollectionFactory;

    /**
     * @var ProductDataBuilder
     */
    protected $productDataBuilder;

    /**
     * @var MarketplaceHelper
     */
    protected $marketplaceHelper;

    /**
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Backend\Model\Session\Quote $sessionQuote
     * @param \Magento\Sales\Model\AdminOrder\Create $orderCreate
     * @param \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
     * @param \Magento\Tax\Helper\Data $taxData
     * @param CollectionFactory $shipTableRatesCollectionFactory
     * @param ProductDataBuilder $productDataBuilder
     * @param MarketplaceHelper $marketplaceHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Backend\Model\Session\Quote $sessionQuote,
        \Magento\Sales\Model\AdminOrder\Create $orderCreate,
        \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency,
        \Magento\Tax\Helper\Data $taxData,
        CollectionFactory $shipTableRatesCollectionFactory,
        ProductDataBuilder $productDataBuilder,
        MarketplaceHelper $marketplaceHelper,
        array $data = []
    ) {
        $this->shipTableRatesCollectionFactory = $shipTableRatesCollectionFactory;
        $this->productDataBuilder = $productDataBuilder;
        $this->marketplaceHelper = $marketplaceHelper;
        parent::__construct($context, $sessionQuote, $orderCreate, $priceCurrency, $taxData, $data);
    }

    /**
     * Custom shipping rate
     *
     * @return string
     */
    public function getActiveCustomShippingRateMethod()
    {
        $rate = $this->getActiveMethodRate();
        return $rate && $rate->getCarrier() == Carrier::CODE ? $rate->getMethod() : '';
    }

    /**
     * Custom shipping rate
     *
     * @return string
     */
    public function getActiveCustomShippingRatePrice()
    {
        $rate = $this->getActiveMethodRate();
        return $this->getActiveCustomShippingRateMethod() && $rate->getPrice() ? $rate->getPrice() * 1 : '';
    }

    /**
     * Custom shipping rate
     *
     * @return string
     */
    public function isCustomShippingRateActive()
    {
        if (empty($this->activeMethodRate)) {
            $this->activeMethodRate = $this->getActiveMethodRate();
        }

        return $this->activeMethodRate && $this->activeMethodRate->getCarrier() == Carrier::CODE ? true : false;
    }

    /**
     * Retrieve array of shipping rates groups
     *
     * @return array
     */
    public function getGroupShippingRates()
    {
        $rates = $this->getShippingRates();

        if (array_key_exists(Carrier::CODE, $rates)) {
            if (!$this->isCustomShippingRateActive()) {
                unset($rates[Carrier::CODE]);
            } else {
                $activeRateMethod = $this->getActiveCustomShippingRateMethod();
                foreach ($rates[Carrier::CODE] as $key => $rate) {
                    if ($rate->getMethod() != $activeRateMethod) {
                        unset($rates[Carrier::CODE][$key]);
                    }
                }
            }
        }

        return $rates;
    }

    /**
     * Get suggested shipping rates based on free shipping thresholds
     *
     * @return array
     */
    public function getSuggestedShippingRates()
    {
        $quote = $this->getQuote();
        if (!$quote || !$quote->getShippingAddress()) {
            return [];
        }

        $items = $quote->getAllVisibleItems();
        $country = $quote->getShippingAddress()->getCountryId();

        if (!$country || empty($items)) {
            return [];
        }

        // Build seller data using the same logic as frontend
        $toShipPerSellerArray = $this->productDataBuilder->build($items, $country);
        $suggestedRates = [];

        foreach ($toShipPerSellerArray as $sellerKey => $sellerData) {
            [$sellerIdentifier, $sellerId] = explode('|', $sellerKey);
            $sellerSubtotal = $sellerData['seller_subtotal'] ?? 0;

            // Get shipping rates for this seller
            $collection = $this->shipTableRatesCollectionFactory->create()
                ->addFieldToFilter('seller_id', ['eq' => $sellerId])
                ->addFieldToFilter('countries', ['finset' => $country])
                ->addFieldToFilter('weight', ['lteq' => $sellerData['total_weight']])
                ->setOrder('weight', 'DESC');

            foreach ($collection as $rate) {
                $serviceType = $rate->getServiceType();
                $minOrderAmount = $rate->getMinOrderAmount();
                $shippingPrice = $rate->getShippingPrice();

                // Check if free shipping threshold applies
                if ($minOrderAmount > 0 && $sellerSubtotal >= $minOrderAmount) {
                    $shippingPrice = 0;
                    $serviceType .= ' (Free Shipping Applied)';
                }

                $suggestedRates[] = [
                    'seller_id' => $sellerId,
                    'service_type' => $serviceType,
                    'shipping_price' => $shippingPrice,
                    'seller_subtotal' => $sellerSubtotal,
                    'threshold_met' => ($minOrderAmount > 0 && $sellerSubtotal >= $minOrderAmount),
                    'min_order_amount' => $minOrderAmount
                ];
            }
        }

        return $suggestedRates;
    }

    /**
     * Get calculated shipping price for admin order
     *
     * @return float
     */
    public function getCalculatedShippingPrice()
    {
        $suggestedRates = $this->getSuggestedShippingRates();
        $totalShipping = 0;

        foreach ($suggestedRates as $rate) {
            $totalShipping += $rate['shipping_price'];
        }

        return $totalShipping;
    }

    /**
     * Check if any seller qualifies for free shipping
     *
     * @return bool
     */
    public function hasFreeShippingAvailable()
    {
        $suggestedRates = $this->getSuggestedShippingRates();

        foreach ($suggestedRates as $rate) {
            if ($rate['threshold_met']) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get free shipping threshold information for display
     *
     * @return array
     */
    public function getFreeShippingInfo()
    {
        $suggestedRates = $this->getSuggestedShippingRates();
        $info = [];

        foreach ($suggestedRates as $rate) {
            if ($rate['min_order_amount'] > 0) {
                $info[] = [
                    'seller_id' => $rate['seller_id'],
                    'current_subtotal' => $rate['seller_subtotal'],
                    'threshold_amount' => $rate['min_order_amount'],
                    'threshold_met' => $rate['threshold_met'],
                    'amount_needed' => max(0, $rate['min_order_amount'] - $rate['seller_subtotal'])
                ];
            }
        }

        return $info;
    }
}
