<?php

declare(strict_types=1);

namespace Coditron\CustomShippingRate\Model\RateProviders;

use Coditron\CustomShippingRate\Api\ShippingRateProviderInterface;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\Collection;
use Coditron\CustomShippingRate\Model\ResourceModel\ShipTableRates\CollectionFactory as TableRatesFactory;
use Coditron\CustomShippingRate\Model\ShipTableRates;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Psr\Log\LoggerInterface;

class RatesByService implements ShippingRateProviderInterface
{
    /**
     * @param TableRatesFactory $ratesCollectionFactory
     * @param LoggerInterface $logger
     * @param array $data
     */
    public function __construct(
        private readonly TableRatesFactory $ratesCollectionFactory,
        private readonly LoggerInterface $logger,
        private readonly array $data = []
    ) {
    }

    /**
     * @param RateRequest $request
     * @param string $sellerIdentifier
     * @param string $sellerId
     * @return array<ShipTableRates[]>
     */
    public function get(RateRequest $request, string $sellerIdentifier, string $sellerId): array
    {
        // Get seller's order subtotal for free shipping threshold calculation
        $sellerSubtotal = $this->data['seller_subtotal'] ?? 0;

        /** @var Collection $tableRatesCollection */
        $tableRatesCollection = $this->ratesCollectionFactory->create()
            ->addFieldToFilter('seller_id', ['eq' => $sellerId])
            ->addFieldToFilter('weight', ['gteq' => $this->data['total_weight']])
            ->addFieldToFilter('countries', ['finset' => $this->data['country']])
            ->addFieldToFilter('service_type', ['in' => $this->data['services']]) // Fetch all service types at once
            ->setOrder('weight', 'ASC');

        $this->logger->info(
            '[CoditronFetchRates] Fetching rates select',
            [
                'query' => $tableRatesCollection->getSelect()->__toString(),
                'seller' => $sellerId,
                'seller_subtotal' => $sellerSubtotal,
            ]
        );

        $tableRatesByServiceType = [];

        if (!$tableRatesCollection->getSize()) {
            return $tableRatesByServiceType;
        }

        /** @var ShipTableRates $rate */
        foreach ($tableRatesCollection->getItems() as $rate) {
            // Check if free shipping threshold applies
            $finalRate = clone $rate;
            if ($this->shouldApplyFreeShipping($rate, $sellerSubtotal)) {
                $finalRate->setShippingPrice('0');
                $this->logger->info(
                    '[CoditronFreeShipping] Applied free shipping threshold',
                    [
                        'seller' => $sellerId,
                        'service_type' => $rate->getServiceType(),
                        'min_order_amount' => $rate->getMinOrderAmount(),
                        'seller_subtotal' => $sellerSubtotal,
                        'original_price' => $rate->getShippingPrice()
                    ]
                );
            }

            $tableRatesByServiceType[$rate->getServiceType()] = $finalRate;
        }

        return $tableRatesByServiceType;
    }

    /**
     * Check if free shipping should be applied based on threshold
     *
     * @param ShipTableRates $rate
     * @param float $sellerSubtotal
     * @return bool
     */
    private function shouldApplyFreeShipping(ShipTableRates $rate, float $sellerSubtotal): bool
    {
        $minOrderAmount = $rate->getMinOrderAmount();

        // If no minimum order amount is set, no free shipping applies
        if (!$minOrderAmount || $minOrderAmount <= 0) {
            return false;
        }

        // Apply free shipping if seller subtotal meets or exceeds the threshold
        return $sellerSubtotal >= $minOrderAmount;
    }
}
